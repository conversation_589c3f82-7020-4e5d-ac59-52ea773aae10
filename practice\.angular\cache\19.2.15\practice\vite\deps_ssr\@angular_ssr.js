import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  AngularAppEngine,
  InlineCriticalCssProcessor,
  PrerenderFallback,
  RenderMode,
  createRequestHandler,
  destroyAngularServerApp,
  extractRoutesAndCreateRouteTree,
  getOrCreateAngularServerApp,
  getRoutesFromAngularRouterConfig,
  provideServerRoutesConfig,
  provideServerRouting,
  setAngularAppEngineManifest,
  setAngularAppManifest,
  withAppShell
} from "./chunk-PGAZQA7T.js";
import "./chunk-DMA5PODP.js";
import "./chunk-WVU5MJXW.js";
import "./chunk-PWDCG4SH.js";
import "./chunk-XKU4W7O3.js";
import "./chunk-D6OAEE7N.js";
import "./chunk-UMQWUUBM.js";
import "./chunk-PXXRCHXC.js";
import "./chunk-YHCV7DAQ.js";
export {
  AngularAppEng<PERSON>,
  PrerenderFallback,
  RenderMode,
  createRequestHandler,
  provideServerRoutesConfig,
  provideServerRouting,
  withAppShell,
  InlineCriticalCssProcessor as ɵInlineCriticalCssProcessor,
  destroyAngularServerApp as ɵdestroyAngularServerApp,
  extractRoutesAndCreateRouteTree as ɵextractRoutesAndCreateRouteTree,
  getOrCreateAngularServerApp as ɵgetOrCreateAngularServerApp,
  getRoutesFromAngularRouterConfig as ɵgetRoutesFromAngularRouterConfig,
  setAngularAppEngineManifest as ɵsetAngularAppEngineManifest,
  setAngularAppManifest as ɵsetAngularAppManifest
};
