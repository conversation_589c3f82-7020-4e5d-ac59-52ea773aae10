import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
 export interface student{
  id?:number;
  name:string;
  mobileNo:number;
 }
@Injectable({
  providedIn: 'root'
})
export class StudentService {
  private apiUrl='https://localhost:7220/api/Student'

  constructor(private http:HttpClient) { }
  getAll():Observable<student[]>{
    return this.http.get<student[]>(this.apiUrl);
  }
  getById(id:number):Observable<student>{
    return this.http.get<student>(`${this.apiUrl}/${id}`);
  }


  
}
