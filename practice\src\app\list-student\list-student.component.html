<h2>StudentList</h2>
<button routerLink="/create">AddStudent</button>
<table border="1">
    <thead>
        <tr>
            <th>Name</th>
            <th>MobileNo</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        <tr *ngFor="let student of students">
            <td>{{student.name}}</td>
            <td>{{student.mobileNo}}</td>
            <td>
                <button (click)="editStudent(student.id!)">Edit</button>
            </td>
            
        </tr>
    </tbody>
</table>