import { Component,OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { StudentService,student } from '../student.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-list-student',
  imports: [CommonModule],
  templateUrl: './list-student.component.html',
  styleUrl: './list-student.component.css',
 
})
export class ListStudentComponent {
students:student[]=[];
constructor(private stuservice:StudentService,private route:Router){}
ngOnint():void{
  this.loadStudents();
}
loadStudents():void{
  this.stuservice.getAll().subscribe(data=>this.students=data);
  
}
editStudent(id:number):void{
  this.route.navigate(['/edit',id]);
}
}
