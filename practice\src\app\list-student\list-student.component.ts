import { Component,OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { StudentService,student } from '../student.service';
import { Router, RouterLink } from '@angular/router';

@Component({
  selector: 'app-list-student',
  imports: [CommonModule, RouterLink],
  templateUrl: './list-student.component.html',
  styleUrl: './list-student.component.css',
 
})
export class ListStudentComponent implements OnInit {
students:student[]=[];
constructor(private stuservice:StudentService,private route:Router){}
ngOnInit():void{
  // Add test data to verify component works
  this.students = [
    { id: 1, name: 'Test Student 1', mobileNo: 1234567890 },
    { id: 2, name: 'Test Student 2', mobileNo: 9876543210 }
  ];

  // Also try to load from API
  this.loadStudents();
}
loadStudents():void{
  console.log('Loading students...');
  this.stuservice.getAll().subscribe({
    next: (data) => {
      console.log('Data received:', data);
      this.students = data;
    },
    error: (error) => {
      console.error('Error fetching students:', error);
    }
  });
}
editStudent(id:number):void{
  this.route.navigate(['/edit',id]);
}
}
